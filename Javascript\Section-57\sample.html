<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .container{
            background-color: red;
            width: 400px;
            margin: auto;
            padding: 10px;
            box-shadow: 10px 10px 3px black;
        }
    </style>
</head>
<body>
    <div class="container">
        <input type="text" placeholder="Enter Input">
        <button onclick="manipulateElements()"> Click me! </button>
        <ol>
            <li>Item-1</li>
            <li>Item-2</li>
            <li>Item-3</li>
        </ol>
    </div>
    <script src="manipulatingElements.js" >

    </script>
</body>
</html>