{"name": "ep18-custom-hook", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.7.7", "bootstrap": "^5.3.3", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-loading-indicators": "^1.0.0", "react-router-dom": "^6.27.0", "styled-components": "^6.1.13"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "vite": "^5.4.9"}}