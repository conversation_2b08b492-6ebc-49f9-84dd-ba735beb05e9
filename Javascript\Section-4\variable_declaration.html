<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
  
</head>
<body>
    <h1> Im gonna make you dynamic </h1>
    <!-- <script >

        //Variable Declaration

        // keyword variable_name = value ;
        var user1 = "Front End Development";
        let email = "<EMAIL>";
        const age = 25;

        console.log( user1 )
        console.log( email )
     </script> -->
     <!-- <script src="index.js">
       
     </script> -->
     <!-- <script src="operators.js">

     </script> -->
     <!-- <script src="concat-template.js">

     </script> -->
     <!-- <script src="type-conversion.js">

     </script> -->
     <script src="variableScopes.js">

     </script>
</body>
</html>