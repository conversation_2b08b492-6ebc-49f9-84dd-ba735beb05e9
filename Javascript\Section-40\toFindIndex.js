let arr = [10,20,30,20,40,50,10]

// let newIndex = arr.indexOf( 10, 2 )
// let newIndex = arr.indexOf( 20, -4 )
// let newIndex = arr.indexOf( 20,0)

// console.log( newIndex );

// let findIndexFromLast = arr.lastIndexOf(10)
// let findIndexFromLast = arr.lastIndexOf(20)
// let findIndexFromLast = arr.lastIndexOf(20,-5)
let findIndexFromLast = arr.lastIndexOf(20,0)

console.log( findIndexFromLast );