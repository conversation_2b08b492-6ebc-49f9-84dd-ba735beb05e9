// var age = 30; // Declaration and Initialization

var age ; // Declaration
age = 40; // Initialization or Assigning

// var age = 70; // Re-declaration
// age = "Eighty" // Re-Initialize or Reassign


console.log( age );

// let newAge = 150; // Declaration and Initialization

let newAge; // Declaration
newAge = 150; // Initialization or Assigning

// let newAge = 30; //Re-Declare is not possible

newAge = "Hundred" // Re-Assign

console.log( newAge );


const employeeName = "xyz";

//  employeeName = 30;

 console.log( employeeName  );


// Printing Statements


alert( employeeName )

document.write( newAge )
document.writeln( newAge )

confirm( newAge );


// let userAge = prompt( "Enter Your Age" );

// console.log( userAge );
// let userAge;
// console.error( userAge )
// console.warn( userAge )

// console.clear()