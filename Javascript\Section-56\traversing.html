<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div class="parent">
        abcd
        <div class="child1">Content-1</div>
        1234
        <div class="child2">Content-2</div>

        <div class="child3">Content-3</div>

        <ol style="background-color: rgb(235, 26, 26); width: 200px; margin: auto;">
            <li> Promise </li>
            <li> Asyn - await </li>
            <li> SetTimeOut </li>
        </ol>

    </div>
    <input type="text">
    <button ondblclick=" selectSiblings() " > Click me </button>
    <button onclick=" insertOne() " > Insert me </button>
    <script src="traversingElements.js">  
    </script>
</body>
</html>