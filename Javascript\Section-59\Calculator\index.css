.calculator{
    width: 30vw;
    height: 70vh;
    border: 1px solid rgb(216, 210, 210);
    margin: auto;
    position: relative;
    top: 100px;
    display: grid;
    gap: 5px;
    padding: 20px;
}
.calculator>.form-group{
    display: flex;
    justify-content: space-between;
    gap: 5px;
}
.calculator>.form-group>button{
    width: 25%  ;
    font-size: 20px;
    border: none;
    border-radius: 5px;
}
.calculator>.form-group>button:hover{
    background-color: rgb(196, 189, 189);
}
.calculator>.form-group>input{
    width: 100%;
    font-size: 40px;
    border: 1px solid rgb(227, 218, 218);
    text-align: end;
    outline: none;
    border-radius: 5px;
    padding-right: 10px;
}
.calculator>.form-group>.evaluate{
    color: aliceblue;
    background-color: rgb(49, 49, 132);
    font-size: 30px;
}
.calculator>.form-group>.evaluate:hover{
    background-color: rgb(73, 73, 177);
   
}