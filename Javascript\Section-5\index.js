let uName = "ECMAScript"

console.log( uName )


let lName = "Javascript"
let LName = "Dynamically Typed"

console.log(lName, LName ); 


let $lName = "Single Page Application"
let _lName = "SPA"
// let 0lName = "SPA" // Numeric value is not allowed as a first letter

let lName1 = "abc"

console.log( $lName, _lName );

//Camel Case
//Pascal Case
//Under Case


let newemployeeid = 10;

let newEmployeeId = 10 //camelCase
let NewEmployeeId = 10 //PascalCase
let New_Employee_Id = 10 //Under_Case
let new_Employee_Id = 10 //Under_Case





