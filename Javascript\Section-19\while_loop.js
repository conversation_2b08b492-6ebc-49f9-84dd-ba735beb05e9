// while loop

/*
    initialization;
    while( condition ){
        statement;
        counter;
    }

 */
    let val = 0; //0

    while(val>=1){
        console.log( val );
        val--;
    }
    
    // let num = 234; //1
    // let sum = 0; //9
    // // 1 + 2 + 3 + 4 = 10
    
    // while( num > 0 ){
    //     let last = num%10 // 1 % 2 => 1
    //     num = parseInt( num/10) // 0
    //     sum = sum + last
    // }
    // console.log( sum );
    // console.log( num%10 );
    // console.log( parseInt (num/10) );
    // 1234 =>  4 => 123 => sum = 4
    // 123 => 3 => 12 => sum = 7
    