<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
  
</head>
<body>
    <h1> Im gonna make you dynamic </h1>
    <script >
        // alert("hii")
        // setTimeout( ()=>{
        //     document.querySelector( "h1" ).innerHTML = "IM changed"
        // }, 4000 )
        // console.log( 100 )
        // console.log( "Javascript" )
        // document.write(  "Javascript INTRO" )

        //Variable Declaration

        // keyword variable_name = value ;
        var user1 = "Front End Development";
        let email = "<EMAIL>";
        const age = 25;

        console.log( user1 )
        console.log( email )
     </script>
      <script src="index.js">
       
     </script> -->
</body>
</html>