
//Return Type

// function findRectArea(l, b){
//     // console.log( "Finding Area" );
//     let condition = false
//     if(condition){
//         return l*b
//     }
//     else{
//         return null
//     }
//     // console.log( "Find Val" );
// }
// let newVal = findRectArea( 20,10 )
// console.log( findRectArea(100, 50), newVal );

// function cubic(num){
//     console.log( num**3 );
//     return ( num**3)
//     return
// }
// let newVal = cubic(4)

// console.log( newVal );

//Non-return Type

function name1() {
    console.log( "Non-return type" );
    return 12
}
// name1()

let noReturn = name1()

console.log( noReturn );