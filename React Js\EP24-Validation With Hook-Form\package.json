{"name": "validation-with-hook-form", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.1", "@mui/material": "^6.1.7", "@reduxjs/toolkit": "^2.3.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-loading-indicators": "^1.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.28.0", "styled-components": "^6.1.13", "sweetalert2": "^11.14.5", "yup": "^1.4.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "vite": "^5.4.10"}}