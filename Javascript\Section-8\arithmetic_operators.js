// Arithmatic Operator


// console.log( 10 + 20 );

// console.log( 20 - 5 ); 

// console.log( 20 * 2 );

// console.log( 20 / 2 ); // Quotient ==> 10 

// console.log( 21 % 5 ); //Modulus -- Remainder ==> 1

// console.log(  3 ** 4  ); // Exponential 

//Increment - Decrement
// let num = 10;

// num = num + 1 // 10 + 1 = 11

// console.log( num  );

//Increment ( ++ )

// let num = 15; // 17
// num++; // Post Increment ==> num = num + 1 = 15 + 1 = 16
// ++num; //Pre Increment ==> num = num + 1 = 16 + 1 = 17

// console.log( num );

// // //Decrement ( -- )

// let num1 = 20; //18

// num1--; // num1 = num1 -1 = 20 - 1 = 19
// --num1; // num1 - 1 = 19 - 1 = 18

// console.log( num1 );