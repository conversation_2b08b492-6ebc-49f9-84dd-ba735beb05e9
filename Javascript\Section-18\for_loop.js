// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );
// console.log( 1 );

//For loop

/*
    for(initialization; condition; counter ){
    }

 */

// for( let i = 1; i<=10; i++ ){ //i = i+1 = 1 + 1 = 11
//     console.log( i );
// }

// for( let i = 1; i<=10; i++ ){
//     // if( i%2 === 0 ) console.log( i );
//     if(  i%2 !== 0 ) console.log( i );
// }