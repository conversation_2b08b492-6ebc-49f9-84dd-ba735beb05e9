
//Assignment Operator

//addition

// let age1 = 20
// let additionalVal = 100;

// age1 += 20 // 20 + 20 = 40
// // age1 = age1 + 20
// age1 += additionalVal // 40 + 100

// age1 -= 10
// age1 *= 3 //age = age * 2
// age1/=2 // age = age/2
// age1%=2
// age1**=2

// console.log( age1 );


//Relational Operator

// console.log(  20 < 20 ); //Less than
// console.log( 21<=20 ); //Less than or equal

// console.log(  40 > 40 ); //Greater than
// console.log(  40 >= 39 ); //Greater than or equal to

// console.log( 40 == '40' ); //Equal to
// console.log( 40 === '50' ); //Strict Equal

// console.log(  40 != '50' ); //not equal
// console.log(  40 !== '40' ); //Struict not equal
// console.log(  40 !== 50 ); //Struict not equal